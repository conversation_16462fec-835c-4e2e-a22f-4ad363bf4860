<script setup lang="ts">
import { addMemoApi, editMemoApi } from '@/api/courseSchedule'

let props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
  currentApiParam: {
    type: Object,
    default: () => {},
  },
})
let emit = defineEmits(['refresh'])
let formRef = $ref<any>(null)
const show = defineModel<boolean>('show')
async function addMemo() {
  const {
    timetableDate,
    timetableTimeIndex,
    timetableTimeType,
  } = props.currentApiParam
  await addMemoApi({
    memoTitle: formOptions.title,
    memoContent: formOptions.content,
    timetableDate,
    timetableTimeIndex,
    timetableTimeType,
  })
  $g.showToast('保存成功')
  emit('refresh')
}
async function editMemo() {
  await editMemoApi({
    memoTitle: formOptions.title,
    memoContent: formOptions.content,
    schoolTeacherMemoId: props.data.schoolTeacherMemoId,
  })
  $g.showToast('保存成功')
  emit('refresh')
}
function confirm() {
  formRef.validate((valid) => {
    if (valid) {
      show.value = false
      if (props.data?.memoTitle)
        editMemo()

      else
        addMemo()
    }
    else {
      console.log('error submit!')
    }
  })
}
let formOptions = $ref<any>({
  title: '',
  content: '',
})
watch(() => show.value, () => {
  if (show.value) {
    if (formRef)
      formRef.resetFields()

    formOptions.title = props.data?.memoTitle || ''
    formOptions.content = props.data?.memoContent || ''
  }
})
</script>

<template>
  <div>
    <el-dialog
      v-model="show"
      class="w-[400px] !px-0"
      align-center
      :show-close="false"
    >
      <div
        class="flex relative w-full text-[17px] font-500 mb-[14px] justify-center items-center"
      >
        {{ props.data?.memoTitle ? '编辑备忘录' : '新增备忘录'
        }}<img
          class="w-[15px] cursor-pointer absolute right-[26px] h-[15px]"
          src="@/assets/img/courseSchedule/close.png"
          @click="show = false"
        />
      </div>
      <div class="px-[17px]">
        <el-form
          ref="formRef"
          :model="formOptions"
          label-width="auto"
        >
          <el-form-item
            :rules="{
              required: true,
              message: '请输入标题',
              trigger: 'blur',
            }"
            label=""
            prop="title"
          >
            <el-input
              v-model="formOptions.title"
              maxlength="15"
              placeholder="请输入标题"
              show-word-limit
              type="text"
            />
          </el-form-item>
          <el-form-item
            :rules="{
              required: true,
              message: '请输入内容',
              trigger: 'blur',
            }"
            label=""
            prop="content"
          >
            <el-input
              v-model="formOptions.content"
              maxlength="1000"
              placeholder="请输入内容"
              show-word-limit
              :rows="7"
              type="textarea"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div>
          <el-button
            class="bg-[#999999]/[0.1048] mr-[17px] w-[75px] h-[30px]"
            @click="show = false"
          >
            取消
          </el-button>
          <el-button
            class="w-[75px] mr-[17px] h-[30px]"
            type="primary"
            @click="confirm"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
</style>
