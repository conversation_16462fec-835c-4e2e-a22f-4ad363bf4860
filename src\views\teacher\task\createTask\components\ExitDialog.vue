<script setup lang="ts">
import { getTaskCancel, setTaskCancel } from '@/api/taskCenter'

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  promiseObj: {
    type: Object, // 外部控制，在外部以promise链式调用，把resolve和reject传进来
  },
})
const emit = defineEmits(['update:show'])
const questionList = inject<Ref<any[]>>('questionList', ref([]))
const resources = inject<Ref<any[]>>('resources', ref([]))
const router = useRouter()
let showDialog = $ref(false)
let checked = $ref(false)
watch(
  () => props.show,
  async (val) => {
    if (!val)
      return

    // if (!questionList.value.length && !resources.value.length) {
    //   quit()
    //   return
    // }

    // 获取30内是否需要提示
    try {
      let res = await getTaskCancel()
      if (!res) {
        quit()
        return
      }

      showDialog = val
    }
    catch {
      quit()
    }
  },
)
watch(
  () => showDialog,
  (val) => {
    emit('update:show', val)
  },
)

function quit() {
  emit('update:show', false)
  if (props.promiseObj?.resolve) {
    props.promiseObj.resolve()
    return
  }
  if ($g.isPC)
    router.replace({ name: 'TeacherHome' })

  else
    $g.flutter('back', true)
}
</script>

<template>
  <div>
    <el-dialog
      v-model="showDialog"
      width="500px"
      @close="() => {
        showDialog = false
        promiseObj?.reject()
      }"
    >
      <template #header>
        <div class="flex items-center">
          <svg-common-tip color="#ff7d2e"></svg-common-tip>
          <span class="mt-2px ml-4px font-600 text-17px">确认退出吗？</span>
        </div>
      </template>
      <div class="ml-22px text-[#333333]">
        退出后，当前的任务数据不保存
      </div>
      <template #footer>
        <div class="flex justify-between">
          <el-checkbox v-model="checked" @change="setTaskCancel">
            30天内不再提示
          </el-checkbox>
          <div>
            <el-button
              @click="() => {
                showDialog = false
                promiseObj?.reject()
              }"
            >
              取消
            </el-button>
            <el-button
              type="primary"
              @click="quit"
            >
              退出
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-dialog) {
  border-radius: 10px !important;
}
</style>
