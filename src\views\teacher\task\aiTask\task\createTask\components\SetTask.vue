<script setup lang="ts">
import { getModeList, oldTeacherTip } from '@/api/aiTask'

const route = useRoute()
const taskData = inject<Ref<any>>('taskData', ref({}))

const ref1 = ref<any>()// 引导提示ref
let open = ref<boolean>(false)// 引导提示弹框

let modeList = $ref<Array<{ id: string, title: string }>>([])

function initData() {
  taskData.value.taskName = `${$g.dayjs().format('MM/DD') + route.query.subjectName}AI课任务`
  taskData.value.publishTime = $g.dayjs().format('YYYY-MM-DD HH:mm')
  taskData.value.finishTime = $g.dayjs().hour(23).minute(59).format('YYYY-MM-DD HH:mm')
  taskData.value.taskSetting = null
  taskData.value.teacherMessage = null
}

const publishTimeShortcuts = [
  {
    text: '立即发布',
    value: () => $g.dayjs().format('YYYY-MM-DD HH:mm'),
  },
]
let finishTimeShortcuts = $ref<any>([
])

const publishTimeFormat = $computed(() => {
  if ($g.dayjs(taskData.value.publishTime).isSame($g.dayjs().format('YYYY-MM-DD HH:mm'), 'minute')) {
    taskData.value.isImmediate = true
    return '立即发布'
  }
  taskData.value.isImmediate = false
  return 'YYYY-MM-DD HH:mm'
})

// 禁用当前时间之前的日期和时间
function disabledDate(time) {
  const now = $g.dayjs().startOf('day') // 获取今天的00:00:00
  const selectedTime = $g.dayjs(time) // 被选择的时间

  // 禁用今天以前的日期
  return selectedTime.isBefore(now, 'day')
}

watch(() => taskData.value.publishTime, (newVal) => {
  taskData.value.finishTime = $g.dayjs(newVal).set('hour', 23).set('minute', 59).format('YYYY-MM-DD HH:mm')
  finishTimeShortcuts = [
    {
      text: `推荐时间${taskData.value.finishTime}`,
      value: () => {
        taskData.value.isUnlimited = false
        return taskData.value.finishTime
      },
    },
    {
      text: '不限时',
      value: () => {
        taskData.value.isUnlimited = true
      },
    },
  ]
}, {
  immediate: true,
})
const finishTimeFormat = $computed(() => {
  if (taskData.value.isUnlimited)
    return '不限时'
  return 'YYYY-MM-DD HH:mm'
})
// 第一次提示弹框
async function firstTips() {
  try {
    let res = await oldTeacherTip({ type: 'TASK_MODE' })
    if (res) {
      setTimeout(() => {
        open.value = true
      }, 2000)
    }
  }
  catch (error) {
    console.log(error)
  }
}
// 模式列表
async function getModeListApi() {
  try {
    let res = await getModeList()
    modeList = res ?? []
  }
  catch (error) {
    console.log(error)
  }
}
// 完成时间自定义
function handleRecommend(val) {
  taskData.value.isUnlimited = !taskData.value.finishTime
}

onMounted(async () => {
  await firstTips()
  getModeListApi()
  initData()
})
</script>

<template>
  <div style="width: 100vw;">
    <div class="text-[15px] text-[#333] leading-[22px] font-600 mb-[17px]">
      2.任务设置
    </div>
    <div class="w-full bg-[#fff] br-[4px] min-h-[509px]">
      <el-form
        :model="taskData"
        label-width="auto"
        class="w-[61.72vw] py-[17px] mx-auto"
      >
        <el-form-item
          label="任务名称"
          required
          class="mb-32px"
        >
          <el-input
            v-model="taskData.taskName"
            maxlength="30"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item
          label="任务时间"
          required
          class="mb-32px"
        >
          <div
            class="h-109px bg-[#F3F3FB] br-[4px] w-full flex px-11px text-13px pt-17px"
          >
            <div
              class="text-13px w-84px truncate h-18px lh-[18px] mt-29px"
            >
              班级名称
            </div>
            <div class="mx-17px">
              <div class="h-18px lh-[18px] mb-5px text-[#929296]">
                发布时间
              </div>
              <el-form-item prop="publishTime">
                <el-date-picker
                  v-model="taskData.publishTime"
                  type="datetime"
                  :show-now="false"
                  value-format="YYYY-MM-DD HH:mm"
                  :disabled-date="disabledDate"
                  :shortcuts="publishTimeShortcuts"
                  :format="publishTimeFormat"
                />
              </el-form-item>
            </div>
            <div>
              <div class="h-18px lh-[18px] mb-5px text-[#929296]">
                要求完成时间
              </div>
              <el-form-item prop="finishTime">
                <el-date-picker
                  v-model="taskData.finishTime"
                  type="datetime"
                  :show-now="false"
                  :disabled-date="disabledDate"
                  :format="finishTimeFormat"
                  value-format="YYYY-MM-DD HH:mm"
                  :shortcuts="finishTimeShortcuts"
                  @change="handleRecommend"
                />
              </el-form-item>
            </div>
          </div>
        </el-form-item>
        <el-form-item

          label="任务设置"
          required
          class="mb-32px"
        >
          <svg-common-question ref="ref1" class="w-13px h-13px mr-[10px]" />
          <el-radio-group v-model="taskData.taskSetting">
            <el-radio
              v-for="it in modeList"
              :key="it.id"
              :value="it.id"
            >
              {{ it.title }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="老师留言" class="mb-32px">
          <el-input
            v-model="taskData.teacherMessage"
            type="textarea"
            maxlength="100"
            show-word-limit
            :autosize="{ minRows: 5 }"
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <el-tour
      v-model="open"
      :close-on-press-escape="false"
      :finish-button-props="{
        text: '完成导览', // 自定义结束按钮文案
        type: 'primary', // 可选按钮样式
      }"
      append-to="#app"
    >
      <el-tour-step
        :target="ref1?.$el"
        placement="right"
        :next-button-props="{
          children: '知道了',
          type: 'primary',
        }"
      >
        <div>
          · 当【管控模式】开启时，学生的学习空间将被精准锁定，仅可接触已设定的课程内容，其余学习资源将被严格限制访问，确保学习目标的专注达成。当设定的课程内容结束时，【管控模式】自动解除。
          <br>
          · 若所选学生时段已被占用，任务即无法发布，发布按钮同步置灰，避免时间撞车。
        </div>
      </el-tour-step>
    </el-tour>
  </div>
</template>

<style lang="scss" scoped>
:deep() {
  .el-form-item__label {
    color: #333;
    font-size: 15px;
  }
  .el-tour__footer {
    display: none !important;
  }

}
</style>
