<script setup lang="ts" name="CreateAiTask">
import { createAiTask, getSystemLevelList, validTime } from '@/api/aiTask'
import { createScheduleTaskApi } from '@/api/courseSchedule'
import { useAiTaskStore } from '@/stores/modules/aiTask'
import ExitDialog from '@/views/teacher/task/createTask/components/ExitDialog.vue'
import CourseContent from './components/CourseContent.vue'
import CreateTask from './components/CreateTask.vue'
import ResourcePopup from './components/ResourcePopup.vue'
import SetTask from './components/SetTask.vue'

const aiTaskStore = useAiTaskStore()
const route = useRoute()
const router = useRouter()

let studentData = ref<any>({
  selectStudentList: [], // 所有选择的学生id总和，已去重
  selectedResource: [], // 选择加入的资源
  classType: null, // 班级类型
  layerType: null,
  bookId: null,
  classId: null, // 布置对象班级
  isLayerOpen: false, // 是否开启分层
  reportId: '', // 报告id
})
let taskData = ref<any>({}) // 任务设置信息
let initClassType = $ref<any>(null)
let showPopup = $ref<boolean>(false)
let showExit = $ref(false) // 退出提示弹框
let promiseObj = $ref<any>()
let step = $ref<number>(1) // 步骤

provide('studentData', studentData)
provide('taskData', taskData)
// 已加入课程数量
let selectedResourceCount = $computed(() => {
  return (list) => {
    let count = 0
    list?.forEach((its) => {
      count += its.selectedResourceList?.length ?? 0
    })
    return count
  }
})
// 下一步按钮disabled
let nextBtnDisabled = $computed(() => {
  return (
    selectedResourceCount(studentData.value.selectedResource) > 0 &&
    studentData.value.selectStudentList.length > 0
  )
})

async function publishTask() {
  try {
    // let classList = getClassList()
    let classList = [{
      schoolClassId: studentData.value.classId,
      arrangeObjectType: 1,
      schoolStudentIds: studentData.value.selectStudentList,
    }]
    // 必填校验
    if (
      !taskData.value.taskName ||
      !taskData.value.publishTime ||
      !taskData.value.finishTime ||
      !taskData.value.taskSetting
    ) {
      $g.showToast('请输入任务名称、任务时间和任务设置')
      return
    }
    if (taskData.value.taskSetting == 2) {
      // 校验管控模式时间
      let res = await validTime({
        releaseTime: taskData.value?.isImmediate
          ? null
          : taskData.value.publishTime, // 推荐日期 null
        requireCompleteTime: taskData.value.isUnlimited
          ? null
          : taskData.value.finishTime, // 不限日期 null
        classList,
      })
      if (res?.length) {
        $g.showToast(
          '这门课的时间跟部分同学已安排的任务撞车啦，请再挑个不冲突的时段吧～',
        )
        return
      }
    }
    // 已选课程
    let selectedResource =
      studentData.value.selectedResource?.[0]?.selectedResourceList
    // 已选课程的预估课时
    let estimateTime =
      selectedResource.reduce(
        (sum, item) => sum + (item.computeDuration || 0),
        0,
      ) / 60
    // 默认selectModification  false
    let catalogIds = Object.keys(aiTaskStore?.modifyMap)
    let catalogList = selectedResource.map(it => ({
      bookCatalogId: it.bookCatalogId,
      selectModification: !!catalogIds?.includes(it.bookCatalogId.toString()),
    }))

    let params: any = {
      sysSubjectId: route.query.subjectId,
      taskName: taskData.value.taskName,
      courseNum: Math.ceil(estimateTime / 45), // 选的课程除以45
      estimateTime,
      releaseTime: taskData.value?.isImmediate
        ? null
        : taskData.value.publishTime, // 推荐日期 null
      requireCompleteTime: taskData.value.isUnlimited
        ? null
        : taskData.value.finishTime, // 不限日期 null
      classType: studentData.value.classType,
      teacherMessage: taskData.value.teacherMessage,
      classList,
      bookId: studentData.value.bookId,
      catalogList,
      taskMode: taskData.value.taskSetting,
      studentLayerTypeList: [],
    }
    /**
     * 学生分层数据
     * isLayerOpen打开时配置studentLayerTypeList参数
     * 有报告id及系统分层，不手动修改用接口的系统分层数据，手动修改了就用状态机的分层数据
     * 无报告id，可以手动分层，提交时用状态机的分层数据
     */
    if (studentData.value.isLayerOpen) {
      const hasLocalLayerData = Array.isArray(aiTaskStore?.studentLevelData) && aiTaskStore.studentLevelData.length > 0
      const getValidLayerList = layerList =>
        Array.isArray(layerList)
          ? layerList
              .map(item => ({
                layerType: item.layerType,
                schoolStudentIdList: Array.isArray(item.studentList)
                  ? item.studentList.map(stu => stu.schoolStudentId)
                  : [],
              }))
              .filter(it => it.layerType && Array.isArray(it.schoolStudentIdList) && it.schoolStudentIdList.length > 0)
          : []

      if (studentData.value.reportId) {
        if (hasLocalLayerData) {
          params.studentLayerTypeList = aiTaskStore.getLayerStudents.filter(
            it => it?.layerType && Array.isArray(it.schoolStudentIdList) && it.schoolStudentIdList.length > 0,
          )
        }
        else {
          const res = await getSystemLevelList({
            schoolClassId: studentData.value.classId,
            sysSubjectId: route.query?.subjectId,
            reportId: studentData.value.reportId,
          })
          params.studentLayerTypeList = getValidLayerList(res?.layerList)
        }
      }
      else if (hasLocalLayerData) {
        params.studentLayerTypeList = aiTaskStore.getLayerStudents.filter(
          it => it?.layerType && Array.isArray(it.schoolStudentIdList) && it.schoolStudentIdList.length > 0,
        )
      }
    }

    let taskId = await createAiTask(params)
    if (route.query.isCourseSchedule) {
      // 建立任务与课程表的关联
      let {
        timetableDate,
        timetableTimeIndex,
        timetableTimeType,
      } = route.query
      await createScheduleTaskApi({
        taskId,
        timetableDate,
        timetableTimeIndex,
        timetableTimeType,
      })
    }
    $g.showToast('布置成功，您可以在任务列表中查看此任务')
    // 清除状态机数据
    aiTaskStore.clearAll()
    if ($g.isPC) {
      router.replace({
        name: 'AiTaskList',
        query: {
          notShowExit: 'true',
          ...route.query,
        },
      })
    }
    else {
      $g.flutter('back', true)
    }
  }
  catch (error) {
    console.log(error)
  }
}

onBeforeRouteLeave((to, from, next) => {
  // 编辑课程不提示
  if (to.name === 'AiTaskEdit' || to.name === 'HierarchicalMgt' || to.query.notShowExit) {
    next()
    return
  }
  aiTaskStore.clearAll()
  showExit = true
  new Promise((resolve, reject) => {
    promiseObj = {
      resolve,
      reject,
    }
  })
    .then(() => {
      next()
    })
    .catch(() => {
      next(false)
    })
})
</script>

<template>
  <div class="p-26px pb-[70px]" style="width: 100vw">
    <g-navbar title="布置课程任务" class="mb-17px"></g-navbar>
    <!-- 布置对象 -->
    <template v-if="step == 1">
      <CreateTask :class-type="initClassType" />
      <CourseContent />
    </template>
    <!-- 任务设置 -->
    <template v-else-if="step == 2">
      <SetTask />
    </template>
    <!-- 底部操作按钮 -->
    <div
      class="h-58px w-full bg-[#fff] fixed left-0 bottom-0 z-10 flex items-center justify-end px-26px"
    >
      <!-- 预估时间 -->
      <div
        v-if="studentData.selectedResource?.[0]?.selectedResourceList"
        class="flex items-center br-4px mr-[13px]"
      >
        <div
          class="px-17px py-4px bg-[#EFEFEF] text-[10px] text-[#666666] leading-[21px] flex items-center "
        >
          预估时间
          <el-tooltip
            class="box-item "
            placement="top"
            trigger="hover"
          >
            <template #content>
              <div>
                <div>
                  预估时长基于内容组件预设耗时计算，供教学安排参考
                </div>
              </div>
            </template>
            <img
              :src="$g.tool.getFileUrl('taskCenter/info.png')"
              alt=""
              class="w-12px h-12px cursor-pointer ml-6px"
            />
          </el-tooltip>
        </div>
        <template
          v-for="(it, index) in studentData.selectedResource?.[0]
            ?.selectedResourceList"
          :key="it"
        >
          <div
            class="px-17px py-4px bg-[#F7F7F7] text-[10px] text-[#666666] leading-[21px]"
          >
            {{ it.bookCatalogName }}:约{{
              Math.ceil(it.computeDuration / 60)
            }}分钟
          </div>
          <div
            v-if="
              index
                !== studentData.selectedResource?.[0]?.selectedResourceList?.length
                - 1
            "
            class="h-[9px]"
            style="
              display: inline-block;
              vertical-align: middle;
              border-right: 1px solid #cdcdcd;
            "
          ></div>
        </template>
      </div>
      <el-button
        type="info"
        plain
        @click="showExit = true"
      >
        取消布置
      </el-button>
      <template v-if="step == 1">
        <el-button
          type="primary"
          plain
          :disabled="selectedResourceCount(studentData.selectedResource) == 0"
          @click="showPopup = true"
        >
          已加入
          {{
            selectedResourceCount(studentData.selectedResource)
              ? `(${selectedResourceCount(studentData.selectedResource)})`
              : ''
          }}
        </el-button>
        <el-button
          type="primary"
          :disabled="!nextBtnDisabled"
          @click="step = 2"
        >
          下一步
        </el-button>
      </template>
      <template v-else>
        <el-button type="primary" @click="step = 1">
          上一步
        </el-button>
        <el-button type="primary" @click="publishTask">
          发布
        </el-button>
      </template>
    </div>
    <!-- 已加入课程抽屉 -->
    <ResourcePopup v-model:show="showPopup" />
    <!-- 退出保存提示 -->
    <ExitDialog v-model:show="showExit" :promise-obj="promiseObj"></ExitDialog>
  </div>
</template>

<style scoped>
</style>
