<script setup lang="ts"  name="ComprehensiveTaskCreate">
import {
  createScheduleName,
  editcheduleName,
  getScheduleDetail,
  publishSchedule,
  taskGroupList,
  taskGroupSort,
  taskGroupTaskToggle,
} from '@/api/comprehensiveTask'
import { getTaskType, getTeacherClassList } from '@/api/taskCenter'
import Draggable from 'vuedraggable'
import TaskItem from './components/TaskItem.vue'

const router = useRouter()
const route = useRoute()

let loading = $ref(true)
let scheduleName = $ref('')
let taskState = $ref(1)
let taskScheduleId = $ref(null)
let taskList = $ref<any>([])
let showDialog = $ref(false)
let checkedType = $ref<any>(null)
let taskTypeList = $ref<any>([])
let list = $ref<any>([])
let classType = $ref<number>(1)// 班级类型-1行政班

let taskData = ref({
  groupName: '',
  releaseTime: $g.dayjs().format('YYYY-MM-DD HH:mm'),
  courseNum: '',
  estimateTime: '' as any,
  requireCompleteTime: '' as any,
  notIsRecommend: false,
  isImmediate: true,
  isUnlimited: false,
  taskPatternType: '',
})
let updatePicker = $ref<any>('')
let requireCompleteShortcuts = $ref<any>([])
let resources = ref<any>([])
let questionList = ref<any>([])
let isChecked = $ref<any>('')

const releaseTimeShortcuts = [
  {
    text: '立即发布',
    value: () => $g.dayjs().format('YYYY-MM-DD HH:mm'),
  },
]
const releaseTimeFormat = $computed(() => {
  if ($g.dayjs(taskData.value.releaseTime).isSame($g.dayjs().format('YYYY-MM-DD HH:mm'), 'minute')) {
    taskData.value.isImmediate = true
    return '立即发布'
  }
  taskData.value.isImmediate = false
  return 'YYYY-MM-DD HH:mm'
})

const recommendedTime = $computed(() => {
  // 如果为试题类型任务
  let minutes = 0
  if ((route.query.pageType as any) != 3) {
    const [objectArr, subjectiveArr] = questionList.value
      .flatMap(item => item.subQuestions)
      .reduce(
        (res, item) => {
          res[[1,2,3].includes(item.subQuestionType)
            ? 0
            : 1].push(item)
          return res
        },
        [[], []],
      )
    minutes = objectArr.length * 2 + subjectiveArr.length * 5
  }
  else {
    minutes = Math.ceil(
      resources.value.reduce(
        (res, item) => res + (item.fileDuration ?? 0),
        0,
      ) / 60,
    )
  }

  let n
  if (!minutes) { // 如果全为文件类资源 默认n为4
    n = 4
  }
  else {
    n = Math.ceil(minutes / 45) // 课时数
  }
  let m = Math.round(n / 2)
  taskData.value.courseNum = n
  taskData.value.estimateTime = minutes || n * 45
  return $g
    .dayjs(taskData.value.releaseTime)
    .endOf('day')
    .add(m - 1, 'day')
    .format('YYYY-MM-DD HH:mm')
})
watch(
  () => recommendedTime,
  () => {
    requireCompleteShortcuts = [
      {
        text: `推荐时间${recommendedTime}`,
        value: () => {
          taskData.value.isUnlimited = false
          return recommendedTime
        },
      },
      {
        text: '不限时',
        value: () => {
          taskData.value.isUnlimited = true
        },
      },
    ]
    updatePicker = new Date()
    if (taskData.value.notIsRecommend) return
    taskData.value.requireCompleteTime = recommendedTime
  },
  {
    immediate: true,
  },
)

// 禁用当前时间之前的日期和时间
function disabledDate(time) {
  const now = $g.dayjs().startOf('day') // 获取今天的00:00:00
  const selectedTime = $g.dayjs(time) // 被选择的时间
  // 禁用今天以前的日期
  return selectedTime.isBefore(now, 'day')
}
function disabledHours() {
  const now = $g.dayjs()
  // 只在今天时限制小时
  if ($g.dayjs(taskData.value.releaseTime).isSame(now, 'day')) {
    const hours: any = []
    for (let i = 0; i < now.hour(); i++)
      hours.push(i)

    return hours
  }
  return []
}
function disabledMinutes(selectedHour) {
  const now = $g.dayjs()
  const selected = $g.dayjs(taskData.value.releaseTime)
  // 只在今天并且小时等于当前小时时限制分钟
  if (selected.isSame(now, 'day') && selectedHour === now.hour()) {
    const minutes: any = []
    for (let i = 0; i < now.minute(); i++)
      minutes.push(i)

    return minutes
  }
  return []
}

const requireCompleteTimeFormat = $computed(() => {
  if (taskData.value.isUnlimited)
    return '不限时'

  if (
    $g
      .dayjs(taskData.value.requireCompleteTime)
      .isSame(recommendedTime, 'minute')
  ) {
    return 'YYYY-MM-DD HH:mm(推荐)'
}

  return 'YYYY-MM-DD HH:mm'
})
function handleRecommend(val) {
  taskData.value.notIsRecommend = !$g.dayjs(val).isSame(recommendedTime, 'minute')
  taskData.value.isUnlimited = !taskData.value.requireCompleteTime
}

// 没有名字不可以去创建，taskState为4 已关闭
const createFlag = $computed(() => {
  return taskScheduleId && taskState != 4
  // return scheduleName.length > 0
})

function handleAddTask() {
  const query = taskTypeList[checkedType].query
  router.push({
    name: 'ComprehensiveTaskPackage',
    query: {
      ...query,
      ...route?.query,
      taskScheduleId,
      selectedClassId: isChecked,
    },
  })
  showDialog = false
  closeDialog()
}

function changeType(idx) {
  if (idx == 3) return
  checkedType = idx
}

/**
 * 获取任务类型
 */
function openDialog() {
  getTaskType().then((data) => {
    taskTypeList = data?.map((v) => {
      return {
        ...v,
        substr: v.id == 1 ? 3 : 2,
        className: v.id == 1 ? 'xkw text-[#0A015B]' : v.id == 2 ? 'xblxt text-[#011D5B]' : v.id == 3 ? 'resource text-[#5B2901]' : 'errorTask text-[#5B1301]',
        routerName: v.id == 4 ? 'ErrorTask' : 'CreateTask',
        query: { pageType: v.id },
      }
    })
  })
}

function closeDialog() {
  showDialog = false
  checkedType = null
}

/**
 * 获取任务组列表
 */
function getTaskGroupList() {
  if (!taskScheduleId) return
  taskGroupList({ taskScheduleId }).then((res) => {
    let tempList = res || []
    // 处理 isLink 属性
    tempList = tempList?.map((item, index) => {
      const nextItem = tempList[index + 1]
      return {
        ...item,
        isLink: nextItem ? item.nextTaskScheduleGroupId === nextItem.taskScheduleGroupId : false,
      }
    })

    taskList = tempList || []
  }).catch((e) => {
      taskList = []
    }).finally(() => {
      loading = false
    })
}

/**
 * 获取名称
 */
function fetchDetail() {
  if (!taskScheduleId) return
  getScheduleDetail({ taskScheduleId }).then((res) => {
    scheduleName = res.scheduleName
    taskState = res.state
    taskData.value.releaseTime = res.releaseTime
    taskData.value.isUnlimited = !res.requireCompleteTime
    taskData.value.requireCompleteTime = taskData.value.isUnlimited ? res.releaseTime : res.requireCompleteTime
  })
}

/**
 * 创建和修改
 */
function saveName() {
  if (!scheduleName || taskState != 1) return
  const URL = taskScheduleId ? editcheduleName : createScheduleName
  try {
    const {
      releaseTime,
      requireCompleteTime,
    } = taskData.value
    URL({
      scheduleName,
      taskScheduleId,
      releaseTime: taskData.value.isImmediate ? null : releaseTime,
      requireCompleteTime: taskData.value.isUnlimited ? null : requireCompleteTime,
      schoolClassIdList: [isChecked],
    }).then((res) => {
      if (res) {
        taskScheduleId = res

        router.replace({
          query: {
            ...route.query,
            taskScheduleId,
          },
        })
      }
    })
  }
  catch (err) {
    showDialog = false
  }
}

/**
 * 最终发布 publishSchedule
 */
function publish() {
  publishSchedule({ taskScheduleId }).then((res) => {
    $g.msg('发布成功')
    router.back()
  })
}

/**
 * 关联
 */
function toggleGroup(item, index) {
  const params = {
    prevTaskScheduleGroupId: item.taskScheduleGroupId,
    nextTaskScheduleGroupId: taskList[index + 1]?.taskScheduleGroupId,
  }
  taskGroupTaskToggle(params).then((res) => {
    item.isLink = !item.isLink
  })
}
function dragEnd() {
  taskGroupSort({ taskScheduleGroupIdList: taskList.map(it => (it?.taskScheduleGroupId ?? '')) })
}
// 获取班级
async function getClassList() {
  let res = await getTeacherClassList({
    classType,
  })
  if (!res) {
    list = []
    return
  }
  list = res.map(it => ({
    ...it,
    id: it.schoolClassId,
    name: it?.sysGradeName + it?.className,
  }))
  if (list.length)
    isChecked = list[0]?.id
    // await getStudent()
    // handleRadioChecked()
}

onMounted(async () => {
  taskScheduleId = route.query.taskScheduleId as any
  if (!taskScheduleId)
    loading = false

  fetchDetail()
  getTaskGroupList()
  await getClassList()
})
onActivated(() => {
  getTaskGroupList()
  fetchDetail()
})
</script>

<template>
  <div class="p-26px  flex flex-col h-full">
    <g-navbar :title="`${taskScheduleId ? '编辑' : '创建'}任务`" class="pb-21px">
    </g-navbar>
    <div class="flex-1 overflow-y-auto no-bar">
      <el-scrollbar>
        <div class="mb-16px p-17px !pb-26px bg-white rounded-[9px]">
          <!-- <div class="mb-17px">
            <span class="font-600">综合任务名称</span>
            <span class="text-[#929296] text-13px ">（任务名称，可重复，限15字符）</span>
          </div> -->
          <div class="font-600 text-[#333] text-15px leading-21px mb-18px">
            基础信息
          </div>
          <div class="flex items-center w-[100%] mb-[17px]">
            <span class="text-[#333] text-[15px] leading-21px">布置对象</span>
            <div class=" flex items-center ml-11px" :class="{ '!my-10px': !$g.isPC }">
              <g-radio
                v-model="isChecked"
                :option="list"
                item-class="px-15px  border border-[#E6E6E6] bg-[#FBFBFB] text-[#666666] text-15px h-34px flex items-center br-[6px]"
                active-item-class="!border-[#646AB4] !bg-[#ECEFFF]"
                show-marker
              />
            </div>
          </div>
          <div class="flex items-center w-[50%] mb-[17px]">
            <span class="text-[#333] text-[15px] leading-21px">任务名称</span>
            <el-input
              v-model.trim="scheduleName"
              show-word-limit
              clearable
              maxlength="15"
              placeholder="输入任务名称"
              class="flex-1 ml-11px"
              :disabled="taskState != 1"
              @blur="saveName"
            />
          </div>
          <!-- <div class="mb-17px mt-17px">
            <span class="font-600">综合任务时间设置</span>
          </div> -->
          <div class="flex items-center w-[50%] mb-[17px]">
            <span class=" text-[#333] text-15px leading-21px ">任务时间</span>
            <el-date-picker
              v-model="taskData.releaseTime"
              type="datetime"
              :show-now="false"
              :shortcuts="releaseTimeShortcuts"
              :format="releaseTimeFormat"
              :disabled-date="disabledDate"
              :disabled-hours="disabledHours"
              :disabled-minutes="disabledMinutes"
              :disabled="taskState != 1"
              value-format="YYYY-MM-DD HH:mm"
              class="flex-1 ml-[11px]"
            />
            <el-date-picker
              :key="updatePicker"
              v-model="taskData.requireCompleteTime"
              type="datetime"
              :show-now="false"
              :format="requireCompleteTimeFormat"
              :shortcuts="requireCompleteShortcuts"
              :disabled-date="disabledDate"
              :disabled-hours="disabledHours"
              :disabled-minutes="disabledMinutes"
              :disabled="taskState != 1"
              class="flex-1 ml-[11px]"
              value-format="YYYY-MM-DD HH:mm"
              @change="handleRecommend"
            />
          </div>
          <!-- <div class="flex items-center w-[50%] ">
            <span>完成时间</span>
            <el-date-picker
              :key="updatePicker"
              v-model="taskData.requireCompleteTime"
              type="datetime"
              :show-now="false"
              :format="requireCompleteTimeFormat"
              :shortcuts="requireCompleteShortcuts"
              :disabled-date="disabledDate"
              :disabled-hours="disabledHours"
              :disabled-minutes="disabledMinutes"
              :disabled="taskState != 1"
              class="flex-1 ml-[11px]"
              value-format="YYYY-MM-DD HH:mm"
              @change="handleRecommend"
            />
          </div> -->

          <!-- <div class="text-center">
            <el-button
              class="text-15px px-22px h-30px mt-17px "
              type="primary"
              :disabled="!scheduleName || taskState != 1"
              @click="saveName"
            >
              保存
            </el-button>
          </div> -->
        </div>

        <div class="p-17px bg-white rounded-[9px_9px_0_0] min-h-[calc(100vh-52px-220px)] overflow-y-auto no-bar" :class="{ '!rounded-[9px] !min-h-[calc(100vh-52px-190px)]': taskState != 1 }">
          <div class="font-600 mb-17px">
            综合任务配置
          </div>

          <g-loading v-if="loading" class="h-200px"></g-loading>

          <template v-if="!loading && taskList?.length">
            <Draggable
              v-model="taskList"
              handle=".handle"
              item-key="taskId"
              ghost-class="ghost"
              :animation="300"
              transition="transform 0.3s ease"
              :disabled="taskState != 1"
              @end="dragEnd"
            >
              <template #item="{ element, index }">
                <div class=" ">
                  <TaskItem
                    :key="element.taskId"
                    :item="element"
                    :index="index"
                    :len="taskList.length"
                    :state="taskState"
                    @toggle-group="toggleGroup"
                    @get-task-list="getTaskGroupList"
                  />
                </div>
              </template>
            </Draggable>
            <el-button
              v-if="taskState == 1 && taskList?.length < 5"
              class="mt-17px w-full text-[#6474FD] border border-[#DADDE8] rounded-[6px] h-[51px] flex justify-center cursor-pointer"
              :class="{ '!cursor-not-allowed !text-[#999] ': !createFlag }"
              :disabled="!createFlag"
              @click="showDialog = true"
            >
              <svg-ri-add-large-line class="mr-9px" />
              <span class="text-[15px]">新增</span>
            </el-button>
          </template>
          <template v-if="!loading && !taskList?.length">
            <div>
              <span class="inline-block mb-17px">任务一</span>
              <el-button
                class="w-full text-[#6474FD] border border-[#DADDE8] rounded-[6px] h-[51px] flex justify-center cursor-pointer"
                :class="{ '!cursor-not-allowed !text-[#999] ': !createFlag }"
                :disabled="!createFlag"
                @click="showDialog = true"
              >
                <svg-ri-add-large-line class="mr-9px" />
                <span class="text-[15px]">新增</span>
              </el-button>
            </div>
          </template>
        </div>
      </el-scrollbar>
    </div>
    <div v-if="taskState == 1" class="foot-bar h-[58px] -mb-26px">
      <el-button @click="router.back()">
        创建任务
      </el-button>
      <el-button
        type="primary"
        class="text-15px px-22px h-30px"
        :disabled="!taskList?.length"
        @click="publish"
      >
        发布
      </el-button>
    </div>

    <el-dialog
      v-model="showDialog"
      title="任务类型选择"
      width="620px"
      center
      :close-on-click-modal="false"
      class="px-26px !rounded-[6px] pt-21px pb-30px"
      @open="openDialog"
      @close="closeDialog"
    >
      <div>
        <div class="flex flex-wrap  mt-27px">
          <div
            v-for="(task, imgIdx) in taskTypeList"
            :key="task.title1"
            class="w-[127px] h-[64px] pl-9px py-7px cursor-pointer rounded-[6px] "
            :class="[
              task.className,
              $g.isPC && task.className.includes('errorTask') ? 'hover:opacity-70' : '',
              task.className?.includes('errorTask') ? '!cursor-not-allowed !border-none' : '',
              imgIdx == checkedType ? `border border-[#6474FD]` : '!border-none',
              imgIdx != taskTypeList?.length - 1 ? `mr-[15px] mb-15px` : '',
            ]"
            @click="changeType(imgIdx)"
          >
            <div class="flex flex-col leading-[19px]">
              <span>{{ task.title?.substr(0, task.substr) }}</span>
              <span>{{ task.title?.substr(task.substr) }}</span>
            </div>
          </div>
        </div>
        <div class="mt-44px flex justify-center">
          <el-button
            type=""
            class="text-15px px-22px h-30px text-[#666] border-none bg-[#F4F4F4]"
            @click="closeDialog"
          >
            取消
          </el-button>
          <el-button
            type="primary"
            class="text-15px px-22px h-30px ml-64px"
            :disabled="checkedType == null"
            @click="handleAddTask"
          >
            确定
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- <CustomSelectionDialog v-model:show="showSelectClassDialog" :class-type="classType" />
    <StudentDrawer v-model="showStudentDrawer"></StudentDrawer> -->
  </div>
</template>

<style lang="scss" scoped>
.foot-bar{
  height: 58px;
  margin: 0 -26px ;
  background: #fff;
  text-align: right;
  box-shadow: 0 -2 7px 0 rgb(0 0 0 / 4%);
  padding-top: 14px;
  padding-right: 26px;
}
.xkw {
  background: url("@/assets/img/taskCenter/xkw_s.png") center / contain no-repeat;
}

.xblxt {
  background: url("@/assets/img/taskCenter/xblxt_s.png") center / contain no-repeat;
}

.resource {
  background: url("@/assets/img/taskCenter/resource_s.png") center / contain
    no-repeat;
}

.errorTask {
  background: url("@/assets/img/taskCenter/errorTask_s.png") center / contain
    no-repeat;
}

.zhrw {
  background: url("@/assets/img/taskCenter/zhrw_s.png") center / contain
    no-repeat;
}
</style>
